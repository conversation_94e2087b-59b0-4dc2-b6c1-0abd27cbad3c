<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\PetHealthRecord;

class DashboardAnalyticsTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $this->user = User::factory()->create([
            'name' => 'Sample User',
            'email' => '<EMAIL>',
            'is_admin' => false,
        ]);
    }

    public function test_dashboard_shows_correct_analytics_with_no_pets()
    {
        $response = $this->actingAs($this->user)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Welcome to PawPortal!');
        $response->assertSee('Add Your First Pet');
    }

    public function test_dashboard_shows_correct_analytics_with_pets()
    {
        // Create test pets
        PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'pet_name' => 'Buddy',
            'species' => 'Dog',
            'last_vaccination' => now()->subMonths(2),
            'medical_history' => 'Healthy dog',
        ]);

        PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'pet_name' => 'Whiskers',
            'species' => 'Cat',
            'last_vaccination' => null,
            'medical_history' => null,
        ]);

        PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'pet_name' => 'Charlie',
            'species' => 'Dog',
            'last_vaccination' => now()->subMonths(8), // Old vaccination
            'medical_history' => 'Senior dog',
        ]);

        $response = $this->actingAs($this->user)->get('/dashboard');
        
        $response->assertStatus(200);
        $response->assertSee('3'); // Total pets
        $response->assertSee('Total Pets');
        $response->assertSee('Vaccination Status');
        $response->assertSee('Medical Records');
        $response->assertSee('Recent Vaccinations');
        $response->assertSee('Pet Species Breakdown');
        $response->assertSee('Dog (2)');
        $response->assertSee('Cat (1)');
    }

    public function test_dashboard_calculates_vaccination_percentage_correctly()
    {
        // Create 2 pets with vaccinations, 1 without
        PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'last_vaccination' => now()->subMonths(2),
        ]);

        PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'last_vaccination' => now()->subMonths(3),
        ]);

        PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'last_vaccination' => null,
        ]);

        $response = $this->actingAs($this->user)->get('/dashboard');
        
        $response->assertStatus(200);
        // Should show 67% (2 out of 3 pets vaccinated)
        $response->assertSee('67%');
    }

    public function test_dashboard_shows_recent_vaccinations_correctly()
    {
        // Create pets with recent and old vaccinations
        PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'last_vaccination' => now()->subMonths(2), // Recent
        ]);

        PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'last_vaccination' => now()->subMonths(8), // Old
        ]);

        $response = $this->actingAs($this->user)->get('/dashboard');
        
        $response->assertStatus(200);
        $response->assertSee('1 pets vaccinated in last 6 months');
    }

    public function test_dashboard_only_shows_user_own_pets()
    {
        $otherUser = User::factory()->create();
        
        // Create pet for current user
        PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'pet_name' => 'My Pet',
        ]);

        // Create pet for other user
        PetHealthRecord::factory()->create([
            'user_id' => $otherUser->id,
            'pet_name' => 'Other Pet',
        ]);

        $response = $this->actingAs($this->user)->get('/dashboard');
        
        $response->assertStatus(200);
        $response->assertSee('Total Pets'); // Should see analytics section
    }
}
