<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PetHealthRecord;
use App\Models\User;

class PetHealthRecordSeeder extends Seeder
{
    public function run()
    {
        // Create a test user if none exists
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sample User',
                'password' => bcrypt('password'),
                'is_admin' => false,
            ]
        );

        // Create sample pet health records
        PetHealthRecord::create([
            'user_id' => $user->id,
            'pet_name' => 'Buddy',
            'species' => 'Dog',
            'breed' => 'Golden Retriever',
            'medical_history' => 'Vaccinated, dewormed, no known allergies',
            'last_vaccination' => '2024-06-15',
        ]);

        PetHealthRecord::create([
            'user_id' => $user->id,
            'pet_name' => 'Whiskers',
            'species' => 'Cat',
            'breed' => 'Persian',
            'medical_history' => 'Spayed, indoor cat, sensitive to fish',
            'last_vaccination' => '2024-05-20',
        ]);

        PetHealthRecord::create([
            'user_id' => $user->id,
            'pet_name' => '<PERSON>',
            'species' => 'Dog',
            'breed' => 'Labrador',
            'medical_history' => 'Hip dysplasia, on medication',
            'last_vaccination' => '2024-07-10',
        ]);
    }
}
